<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Carbon\Carbon;

class Assignment extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'assignment_code',
        'description',
        'instructions',
        'academic_year_id',
        'academic_term_id',
        'subject_id',
        'class_id',
        'section_id',
        'teacher_id',
        'grade_category_id',
        'grade_scale_id',
        'assigned_date',
        'due_date',
        'due_time',
        'total_marks',
        'passing_marks',
        'submission_type',
        'attachments',
        'allow_late_submission',
        'late_penalty_percentage',
        'max_attempts',
        'status',
        'is_published',
        'created_by',
    ];

    protected $casts = [
        'assigned_date' => 'date',
        'due_date' => 'date',
        'due_time' => 'datetime:H:i',
        'total_marks' => 'decimal:2',
        'passing_marks' => 'decimal:2',
        'attachments' => 'array',
        'allow_late_submission' => 'boolean',
        'late_penalty_percentage' => 'decimal:2',
        'max_attempts' => 'integer',
        'is_published' => 'boolean',
    ];

    /**
     * Get the academic year.
     */
    public function academicYear(): BelongsTo
    {
        return $this->belongsTo(AcademicYear::class);
    }

    /**
     * Get the academic term.
     */
    public function academicTerm(): BelongsTo
    {
        return $this->belongsTo(AcademicTerm::class);
    }

    /**
     * Get the subject.
     */
    public function subject(): BelongsTo
    {
        return $this->belongsTo(Subject::class);
    }

    /**
     * Get the class.
     */
    public function class(): BelongsTo
    {
        return $this->belongsTo(ClassModel::class, 'class_id');
    }

    /**
     * Get the class room (alias for class).
     */
    public function classRoom(): BelongsTo
    {
        return $this->belongsTo(ClassModel::class, 'class_id');
    }

    /**
     * Get the section.
     */
    public function section(): BelongsTo
    {
        return $this->belongsTo(Section::class);
    }

    /**
     * Get the teacher.
     */
    public function teacher(): BelongsTo
    {
        return $this->belongsTo(User::class, 'teacher_id');
    }

    /**
     * Get the grade category.
     */
    public function gradeCategory(): BelongsTo
    {
        return $this->belongsTo(GradeCategory::class);
    }

    /**
     * Get the grade scale.
     */
    public function gradeScale(): BelongsTo
    {
        return $this->belongsTo(GradeScale::class);
    }

    /**
     * Get the submissions.
     */
    public function submissions(): HasMany
    {
        return $this->hasMany(AssignmentSubmission::class);
    }

    /**
     * Get the grades for this assignment.
     */
    public function grades(): MorphMany
    {
        return $this->morphMany(Grade::class, 'gradeable');
    }

    /**
     * Scope for published assignments.
     */
    public function scopePublished($query)
    {
        return $query->where('is_published', true);
    }

    /**
     * Scope for current academic year.
     */
    public function scopeCurrentYear($query)
    {
        $currentYear = AcademicYear::current();
        if ($currentYear) {
            return $query->where('academic_year_id', $currentYear->id);
        }
        return $query;
    }

    /**
     * Get status badge color.
     */
    public function getStatusBadgeColorAttribute(): string
    {
        return match($this->status) {
            'draft' => 'badge-gray',
            'assigned' => 'badge-blue',
            'completed' => 'badge-green',
            'cancelled' => 'badge-red',
            default => 'badge-gray',
        };
    }

    /**
     * Get formatted assigned date.
     */
    public function getFormattedAssignedDateAttribute(): string
    {
        return $this->assigned_date->format('M d, Y');
    }

    /**
     * Get formatted due date.
     */
    public function getFormattedDueDateAttribute(): string
    {
        return $this->due_date->format('M d, Y');
    }

    /**
     * Get formatted due datetime.
     */
    public function getFormattedDueDateTimeAttribute(): string
    {
        $dateStr = $this->due_date->format('M d, Y');
        if ($this->due_time) {
            $dateStr .= ' at ' . $this->due_time->format('g:i A');
        }
        return $dateStr;
    }

    /**
     * Check if assignment is overdue.
     */
    public function getIsOverdueAttribute(): bool
    {
        $dueDateTime = $this->due_date;
        if ($this->due_time) {
            $dueDateTime = $this->due_date->setTimeFromTimeString($this->due_time->format('H:i:s'));
        }
        return $dueDateTime->isPast();
    }

    /**
     * Get days until due.
     */
    public function getDaysUntilDueAttribute(): int
    {
        return now()->diffInDays($this->due_date, false);
    }

    /**
     * Get submission statistics.
     */
    public function getSubmissionStatsAttribute(): array
    {
        $totalSubmissions = $this->submissions()->count();
        $gradedSubmissions = $this->submissions()->where('status', 'graded')->count();
        
        return [
            'total' => $totalSubmissions,
            'graded' => $gradedSubmissions,
            'pending' => $totalSubmissions - $gradedSubmissions,
        ];
    }
}
